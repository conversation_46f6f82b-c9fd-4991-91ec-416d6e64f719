-- Useful SQL Queries for Baby's First Year Website
USE baby_first_year;

-- ========================================
-- USER AUTHENTICATION QUERIES
-- ========================================

-- Login user (check credentials)
SELECT id, name, email, baby_name, baby_birthdate 
FROM users 
WHERE email = '<EMAIL>' AND password = '$2b$10$hash1';

-- Register new user
INSERT INTO users (name, email, password, baby_name, baby_birthdate) 
VALUES ('New Parent', '<EMAIL>', '$2b$10$hashedpassword', 'Baby Name', '2024-06-01');

-- Update user profile
UPDATE users 
SET name = 'Updated Name', baby_name = 'Updated Baby Name', baby_birthdate = '2024-06-15'
WHERE id = 1;

-- ========================================
-- DASHBOARD OVERVIEW QUERIES
-- ========================================

-- Get user's baby age in days
SELECT 
    name,
    baby_name,
    baby_birthdate,
    DATEDIFF(CURDATE(), baby_birthdate) as age_in_days,
    FLOOR(DATEDIFF(CURDATE(), baby_birthdate) / 30) as age_in_months
FROM users 
WHERE id = 1;

-- Get today's activity summary for user
SELECT 
    activity_type,
    COUNT(*) as count,
    GROUP_CONCAT(TIME_FORMAT(activity_time, '%H:%i') ORDER BY activity_time) as times
FROM baby_activities 
WHERE user_id = 1 AND activity_date = CURDATE()
GROUP BY activity_type;

-- Get recent activities (last 7 days)
SELECT 
    activity_type,
    activity_date,
    activity_time,
    notes
FROM baby_activities 
WHERE user_id = 1 AND activity_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY activity_date DESC, activity_time DESC
LIMIT 20;

-- ========================================
-- MILESTONE TRACKING QUERIES
-- ========================================

-- Get all milestones with user's progress
SELECT 
    m.id,
    m.milestone_name,
    m.age_range,
    m.description,
    m.category,
    CASE WHEN um.id IS NOT NULL THEN 1 ELSE 0 END as achieved,
    um.achieved_date,
    um.notes as achievement_notes
FROM milestones m
LEFT JOIN user_milestones um ON m.id = um.milestone_id AND um.user_id = 1
ORDER BY m.sort_order;

-- Get milestone progress percentage
SELECT 
    COUNT(m.id) as total_milestones,
    COUNT(um.id) as achieved_milestones,
    ROUND((COUNT(um.id) / COUNT(m.id)) * 100, 2) as progress_percentage
FROM milestones m
LEFT JOIN user_milestones um ON m.id = um.milestone_id AND um.user_id = 1;

-- Mark milestone as achieved
INSERT INTO user_milestones (user_id, milestone_id, achieved_date, notes)
VALUES (1, 5, CURDATE(), 'Baby achieved this milestone today!')
ON DUPLICATE KEY UPDATE 
    achieved_date = VALUES(achieved_date),
    notes = VALUES(notes);

-- Get milestones by age range
SELECT * FROM milestones WHERE age_range = '0-3' ORDER BY sort_order;

-- ========================================
-- ACTIVITY TRACKING QUERIES
-- ========================================

-- Log new activity
INSERT INTO baby_activities (user_id, activity_type, activity_date, activity_time, notes)
VALUES (1, 'feeding', CURDATE(), CURTIME(), 'Breast milk feeding');

-- Get feeding schedule for today
SELECT 
    activity_time,
    notes
FROM baby_activities 
WHERE user_id = 1 
    AND activity_type = 'feeding' 
    AND activity_date = CURDATE()
ORDER BY activity_time;

-- Get sleep pattern for last week
SELECT 
    activity_date,
    COUNT(*) as sleep_sessions,
    MIN(activity_time) as first_sleep,
    MAX(activity_time) as last_sleep
FROM baby_activities 
WHERE user_id = 1 
    AND activity_type = 'sleep' 
    AND activity_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY activity_date
ORDER BY activity_date DESC;

-- Get activity statistics for current month
SELECT 
    activity_type,
    COUNT(*) as total_count,
    AVG(COUNT(*)) OVER (PARTITION BY activity_type) as daily_average
FROM baby_activities 
WHERE user_id = 1 
    AND YEAR(activity_date) = YEAR(CURDATE()) 
    AND MONTH(activity_date) = MONTH(CURDATE())
GROUP BY activity_type, activity_date;

-- ========================================
-- NOTES MANAGEMENT QUERIES
-- ========================================

-- Get all user notes
SELECT 
    id,
    note_title,
    note_content,
    note_date,
    created_at
FROM user_notes 
WHERE user_id = 1 
ORDER BY note_date DESC, created_at DESC;

-- Add new note
INSERT INTO user_notes (user_id, note_title, note_content, note_date)
VALUES (1, 'Today\'s Observation', 'Baby was very active today and smiled a lot!', CURDATE());

-- Update existing note
UPDATE user_notes 
SET note_title = 'Updated Title', note_content = 'Updated content', updated_at = CURRENT_TIMESTAMP
WHERE id = 1 AND user_id = 1;

-- Delete note
DELETE FROM user_notes WHERE id = 1 AND user_id = 1;

-- Search notes by content
SELECT * FROM user_notes 
WHERE user_id = 1 AND (note_title LIKE '%smile%' OR note_content LIKE '%smile%')
ORDER BY note_date DESC;

-- ========================================
-- GROWTH TRACKING QUERIES
-- ========================================

-- Get latest growth measurements
SELECT 
    measurement_date,
    weight_kg,
    height_cm,
    head_circumference_cm,
    notes
FROM growth_tracking 
WHERE user_id = 1 
ORDER BY measurement_date DESC 
LIMIT 1;

-- Get growth history
SELECT 
    measurement_date,
    weight_kg,
    height_cm,
    head_circumference_cm,
    LAG(weight_kg) OVER (ORDER BY measurement_date) as prev_weight,
    LAG(height_cm) OVER (ORDER BY measurement_date) as prev_height
FROM growth_tracking 
WHERE user_id = 1 
ORDER BY measurement_date;

-- Add new growth measurement
INSERT INTO growth_tracking (user_id, measurement_date, weight_kg, height_cm, head_circumference_cm, notes)
VALUES (1, CURDATE(), 6.5, 62.0, 41.0, '4 month checkup measurements');

-- ========================================
-- VACCINATION TRACKING QUERIES
-- ========================================

-- Get vaccination history
SELECT 
    vaccine_name,
    vaccination_date,
    next_due_date,
    doctor_name,
    clinic_name
FROM vaccinations 
WHERE user_id = 1 
ORDER BY vaccination_date DESC;

-- Get upcoming vaccinations
SELECT 
    vaccine_name,
    next_due_date,
    DATEDIFF(next_due_date, CURDATE()) as days_until_due
FROM vaccinations 
WHERE user_id = 1 
    AND next_due_date >= CURDATE()
ORDER BY next_due_date;

-- Add vaccination record
INSERT INTO vaccinations (user_id, vaccine_name, vaccination_date, next_due_date, doctor_name, clinic_name)
VALUES (1, 'DTaP, IPV, Hib, PCV13 (2nd dose)', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 2 MONTH), 'Dr. Smith', 'City Health Center');

-- ========================================
-- APPOINTMENT MANAGEMENT QUERIES
-- ========================================

-- Get upcoming appointments
SELECT 
    appointment_date,
    appointment_time,
    doctor_name,
    clinic_name,
    appointment_type,
    status
FROM appointments 
WHERE user_id = 1 
    AND appointment_date >= CURDATE()
    AND status = 'scheduled'
ORDER BY appointment_date, appointment_time;

-- Schedule new appointment
INSERT INTO appointments (user_id, appointment_date, appointment_time, doctor_name, clinic_name, appointment_type)
VALUES (1, '2025-02-15', '10:00:00', 'Dr. Smith', 'City Health Center', '5 Month Checkup');

-- Update appointment status
UPDATE appointments 
SET status = 'completed', notes = 'Regular checkup completed. Baby is healthy.'
WHERE id = 1 AND user_id = 1;

-- ========================================
-- REPORTING QUERIES
-- ========================================

-- Weekly activity report
SELECT 
    WEEK(activity_date) as week_number,
    activity_type,
    COUNT(*) as activity_count,
    AVG(COUNT(*)) OVER (PARTITION BY activity_type) as weekly_average
FROM baby_activities 
WHERE user_id = 1 
    AND activity_date >= DATE_SUB(CURDATE(), INTERVAL 4 WEEK)
GROUP BY WEEK(activity_date), activity_type
ORDER BY week_number DESC, activity_type;

-- Monthly milestone achievements
SELECT 
    YEAR(um.achieved_date) as year,
    MONTH(um.achieved_date) as month,
    COUNT(*) as milestones_achieved,
    GROUP_CONCAT(m.milestone_name SEPARATOR ', ') as achieved_milestones
FROM user_milestones um
JOIN milestones m ON um.milestone_id = m.id
WHERE um.user_id = 1
GROUP BY YEAR(um.achieved_date), MONTH(um.achieved_date)
ORDER BY year DESC, month DESC;

-- Growth progress report
SELECT 
    measurement_date,
    weight_kg,
    height_cm,
    weight_kg - LAG(weight_kg) OVER (ORDER BY measurement_date) as weight_gain,
    height_cm - LAG(height_cm) OVER (ORDER BY measurement_date) as height_gain,
    DATEDIFF(measurement_date, LAG(measurement_date) OVER (ORDER BY measurement_date)) as days_between
FROM growth_tracking 
WHERE user_id = 1 
ORDER BY measurement_date;

-- ========================================
-- ADMIN/ANALYTICS QUERIES
-- ========================================

-- User activity summary (all users)
SELECT 
    u.name,
    u.baby_name,
    COUNT(ba.id) as total_activities,
    MAX(ba.activity_date) as last_activity_date
FROM users u
LEFT JOIN baby_activities ba ON u.id = ba.user_id
GROUP BY u.id, u.name, u.baby_name
ORDER BY total_activities DESC;

-- Most achieved milestones
SELECT 
    m.milestone_name,
    m.age_range,
    COUNT(um.id) as times_achieved,
    ROUND((COUNT(um.id) / (SELECT COUNT(*) FROM users)) * 100, 2) as achievement_percentage
FROM milestones m
LEFT JOIN user_milestones um ON m.id = um.milestone_id
GROUP BY m.id, m.milestone_name, m.age_range
ORDER BY times_achieved DESC;

-- Database statistics
SELECT 
    'Users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'Activities', COUNT(*) FROM baby_activities
UNION ALL
SELECT 'Milestones', COUNT(*) FROM milestones
UNION ALL
SELECT 'User Milestones', COUNT(*) FROM user_milestones
UNION ALL
SELECT 'Notes', COUNT(*) FROM user_notes
UNION ALL
SELECT 'Growth Records', COUNT(*) FROM growth_tracking;
