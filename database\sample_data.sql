-- Sample Data for Baby's First Year Database
USE baby_first_year;

-- Insert sample users
INSERT INTO users (name, email, password, baby_name, baby_birthdate) VALUES
('<PERSON>', '<EMAIL>', '$2b$10$hash1', '<PERSON>', '2024-01-15'),
('<PERSON>', '<EMAIL>', '$2b$10$hash2', '<PERSON>', '2024-03-20'),
('<PERSON>', '<EMAIL>', '$2b$10$hash3', 'Sweet Ana', '2023-12-10'),
('<PERSON>', '<EMAIL>', '$2b$10$hash4', '<PERSON>', '2024-02-28'),
('<PERSON>', '<EMAIL>', '$2b$10$hash5', '<PERSON>', '2024-04-05');

-- Insert milestones data
INSERT INTO milestones (milestone_name, age_range, description, category, sort_order) VALUES
-- 0-3 months
('Lifts head when on tummy', '0-3', 'Baby can lift head briefly when lying on stomach', 'physical', 1),
('Follows objects with eyes', '0-3', 'Tracks moving objects with eyes', 'cognitive', 2),
('Begins to smile', '0-3', 'Shows first social smiles', 'social', 3),
('Makes cooing sounds', '0-3', 'Produces vowel-like sounds', 'language', 4),
('Holds head steady', '0-3', 'Can hold head up when supported', 'physical', 5),

-- 4-6 months
('Rolls over', '4-6', 'Rolls from tummy to back or back to tummy', 'physical', 6),
('Sits with support', '4-6', 'Can sit upright with help', 'physical', 7),
('Reaches for toys', '4-6', 'Intentionally reaches for and grasps objects', 'cognitive', 8),
('Babbles and laughs', '4-6', 'Makes babbling sounds and laughs', 'language', 9),
('Recognizes familiar faces', '4-6', 'Shows recognition of family members', 'social', 10),

-- 7-9 months
('Sits without support', '7-9', 'Sits independently without falling over', 'physical', 11),
('Crawls or scoots', '7-9', 'Moves around by crawling or scooting', 'physical', 12),
('Says mama or dada', '7-9', 'First meaningful words', 'language', 13),
('Plays peek-a-boo', '7-9', 'Enjoys and participates in peek-a-boo games', 'social', 14),
('Transfers objects between hands', '7-9', 'Can pass toys from one hand to another', 'cognitive', 15),

-- 10-12 months
('Pulls to standing', '10-12', 'Pulls self up to standing position', 'physical', 16),
('Takes first steps', '10-12', 'Walks independently or with minimal support', 'physical', 17),
('Says first words', '10-12', 'Uses words with meaning beyond mama/dada', 'language', 18),
('Waves goodbye', '10-12', 'Waves bye-bye appropriately', 'social', 19),
('Follows simple commands', '10-12', 'Understands and follows simple instructions', 'cognitive', 20);

-- Insert sample baby activities
INSERT INTO baby_activities (user_id, activity_type, activity_date, activity_time, notes) VALUES
-- Maria's baby activities
(1, 'feeding', '2024-12-20', '08:00:00', 'Morning feeding - breast milk'),
(1, 'feeding', '2024-12-20', '12:00:00', 'Lunch feeding - formula'),
(1, 'sleep', '2024-12-20', '14:00:00', 'Afternoon nap - 2 hours'),
(1, 'diaper', '2024-12-20', '09:30:00', 'Wet diaper'),
(1, 'play', '2024-12-20', '16:00:00', 'Tummy time - 15 minutes'),

-- Juan's baby activities
(2, 'feeding', '2024-12-20', '07:30:00', 'Morning bottle'),
(2, 'sleep', '2024-12-20', '13:30:00', 'Long nap'),
(2, 'diaper', '2024-12-20', '10:00:00', 'Dirty diaper'),
(2, 'play', '2024-12-20', '15:30:00', 'Playing with toys'),

-- Ana's baby activities
(3, 'feeding', '2024-12-20', '08:15:00', 'Solid food - banana'),
(3, 'feeding', '2024-12-20', '12:30:00', 'Lunch - rice cereal'),
(3, 'sleep', '2024-12-20', '14:30:00', 'Short nap'),
(3, 'diaper', '2024-12-20', '11:00:00', 'Regular change'),
(3, 'play', '2024-12-20', '17:00:00', 'Crawling practice');

-- Insert sample user milestones
INSERT INTO user_milestones (user_id, milestone_id, achieved_date, notes) VALUES
-- Maria's baby milestones
(1, 1, '2024-02-01', 'First time lifting head during tummy time'),
(1, 2, '2024-02-15', 'Started following colorful toys'),
(1, 3, '2024-02-20', 'First real smile!'),
(1, 4, '2024-03-01', 'Making cute cooing sounds'),

-- Juan's baby milestones
(2, 1, '2024-04-10', 'Good head control'),
(2, 2, '2024-04-25', 'Tracks objects well'),
(2, 3, '2024-05-01', 'Smiling a lot now'),

-- Ana's baby milestones (older baby)
(3, 1, '2024-01-15', 'Early head lifting'),
(3, 2, '2024-01-30', 'Great eye tracking'),
(3, 3, '2024-02-05', 'Social smiles'),
(3, 4, '2024-02-20', 'Cooing sounds'),
(3, 5, '2024-03-01', 'Steady head control'),
(3, 6, '2024-04-15', 'First roll over!'),
(3, 7, '2024-05-01', 'Sitting with support'),
(3, 8, '2024-05-15', 'Reaching for toys'),
(3, 11, '2024-08-01', 'Sitting independently'),
(3, 12, '2024-09-15', 'Started crawling');

-- Insert sample user notes
INSERT INTO user_notes (user_id, note_title, note_content, note_date) VALUES
(1, 'First Smile', 'Baby smiled for the first time today! It was the most beautiful moment.', '2024-02-20'),
(1, 'Sleep Schedule', 'Starting to establish a better sleep routine. Baby is sleeping 3-4 hours at a time.', '2024-03-01'),
(1, 'Doctor Visit', 'Pediatrician says baby is developing well. Weight is on track.', '2024-03-15'),

(2, 'Feeding Notes', 'Baby is drinking 4oz every 3 hours. Seems satisfied after each feeding.', '2024-05-01'),
(2, 'Tummy Time', 'Baby is getting stronger during tummy time. Can lift head for 30 seconds now.', '2024-05-10'),

(3, 'Crawling Progress', 'Baby is getting faster at crawling! Need to baby-proof the house soon.', '2024-09-20'),
(3, 'First Words', 'I think baby said "mama" today! Not sure if it was intentional but it sounded clear.', '2024-10-01'),
(3, 'Solid Foods', 'Introduced banana today. Baby loved it! Made a mess but ate most of it.', '2024-06-15');

-- Insert sample growth tracking
INSERT INTO growth_tracking (user_id, measurement_date, weight_kg, height_cm, head_circumference_cm, notes) VALUES
-- Maria's baby growth
(1, '2024-01-15', 3.2, 50.0, 35.0, 'Birth measurements'),
(1, '2024-02-15', 4.1, 54.0, 37.0, '1 month checkup'),
(1, '2024-03-15', 5.2, 58.0, 39.0, '2 month checkup'),
(1, '2024-04-15', 6.1, 61.0, 40.5, '3 month checkup'),

-- Juan's baby growth
(2, '2024-03-20', 3.4, 51.0, 36.0, 'Birth measurements'),
(2, '2024-04-20', 4.3, 55.0, 38.0, '1 month checkup'),
(2, '2024-05-20', 5.4, 59.0, 40.0, '2 month checkup'),

-- Ana's baby growth (older baby)
(3, '2023-12-10', 3.0, 49.0, 34.5, 'Birth measurements'),
(3, '2024-01-10', 3.8, 53.0, 36.5, '1 month checkup'),
(3, '2024-02-10', 4.9, 57.0, 38.5, '2 month checkup'),
(3, '2024-03-10', 5.8, 60.0, 40.0, '3 month checkup'),
(3, '2024-06-10', 7.2, 67.0, 43.0, '6 month checkup'),
(3, '2024-09-10', 8.5, 72.0, 45.0, '9 month checkup'),
(3, '2024-12-10', 9.8, 76.0, 46.5, '12 month checkup');

-- Insert sample vaccinations
INSERT INTO vaccinations (user_id, vaccine_name, vaccination_date, next_due_date, doctor_name, clinic_name) VALUES
(1, 'Hepatitis B', '2024-01-15', '2024-02-15', 'Dr. Smith', 'City Health Center'),
(1, 'DTaP, IPV, Hib, PCV13', '2024-03-15', '2024-05-15', 'Dr. Smith', 'City Health Center'),

(2, 'Hepatitis B', '2024-03-20', '2024-04-20', 'Dr. Johnson', 'Metro Clinic'),
(2, 'DTaP, IPV, Hib, PCV13', '2024-05-20', '2024-07-20', 'Dr. Johnson', 'Metro Clinic'),

(3, 'Hepatitis B', '2023-12-10', '2024-01-10', 'Dr. Brown', 'Family Health Clinic'),
(3, 'DTaP, IPV, Hib, PCV13', '2024-02-10', '2024-04-10', 'Dr. Brown', 'Family Health Clinic'),
(3, 'DTaP, IPV, Hib, PCV13 (2nd)', '2024-04-10', '2024-06-10', 'Dr. Brown', 'Family Health Clinic'),
(3, 'DTaP, IPV, Hib, PCV13 (3rd)', '2024-06-10', '2024-12-10', 'Dr. Brown', 'Family Health Clinic'),
(3, 'MMR, Varicella', '2024-12-10', '2025-12-10', 'Dr. Brown', 'Family Health Clinic');

-- Insert sample appointments
INSERT INTO appointments (user_id, appointment_date, appointment_time, doctor_name, clinic_name, appointment_type, status) VALUES
(1, '2024-12-25', '10:00:00', 'Dr. Smith', 'City Health Center', 'Regular Checkup', 'scheduled'),
(1, '2025-01-15', '14:30:00', 'Dr. Smith', 'City Health Center', '4 Month Checkup', 'scheduled'),

(2, '2024-12-22', '09:00:00', 'Dr. Johnson', 'Metro Clinic', '3 Month Checkup', 'scheduled'),
(2, '2025-01-20', '11:00:00', 'Dr. Johnson', 'Metro Clinic', 'Vaccination', 'scheduled'),

(3, '2024-12-15', '15:00:00', 'Dr. Brown', 'Family Health Clinic', '12 Month Checkup', 'completed'),
(3, '2025-03-10', '10:30:00', 'Dr. Brown', 'Family Health Clinic', '15 Month Checkup', 'scheduled');

-- Insert sample tips and articles
INSERT INTO tips_articles (title, content, category, age_range, author) VALUES
('Safe Sleep Guidelines', 'Always place your baby on their back to sleep. Use a firm sleep surface and keep the crib bare - no blankets, pillows, or toys.', 'sleep', '0-12', 'Dr. Sarah Wilson'),
('Breastfeeding Tips', 'Ensure proper latch and feed on demand. Most newborns feed 8-12 times per day.', 'feeding', '0-6', 'Lactation Consultant Mary'),
('Tummy Time Benefits', 'Tummy time helps strengthen neck and shoulder muscles. Start with 3-5 minutes several times a day.', 'development', '0-6', 'Physical Therapist John'),
('Baby Proofing Basics', 'Cover electrical outlets, secure cabinets, and install safety gates before baby becomes mobile.', 'safety', '6-12', 'Safety Expert Lisa'),
('Introducing Solid Foods', 'Start with single-ingredient foods around 6 months. Watch for signs of readiness like sitting up and showing interest in food.', 'feeding', '6-12', 'Pediatric Nutritionist Dr. Kim');

-- Insert sample user preferences
INSERT INTO user_preferences (user_id, timezone, weight_unit, height_unit, notifications_enabled) VALUES
(1, 'Asia/Manila', 'kg', 'cm', TRUE),
(2, 'Asia/Manila', 'kg', 'cm', TRUE),
(3, 'Asia/Manila', 'kg', 'cm', FALSE),
(4, 'Asia/Manila', 'lbs', 'inches', TRUE),
(5, 'UTC', 'kg', 'cm', TRUE);

-- Insert sample sleep tracking
INSERT INTO sleep_tracking (user_id, sleep_date, bedtime, wake_time, total_sleep_hours, nap_count, sleep_quality, notes) VALUES
(1, '2024-12-19', '20:00:00', '06:00:00', 10.0, 3, 'good', 'Slept well with 3 short naps during the day'),
(1, '2024-12-20', '19:30:00', '05:30:00', 10.0, 2, 'excellent', 'Great night sleep, only woke up twice for feeding'),

(2, '2024-12-19', '21:00:00', '07:00:00', 10.0, 2, 'fair', 'Woke up several times during the night'),
(2, '2024-12-20', '20:30:00', '06:30:00', 10.0, 3, 'good', 'Better sleep pattern emerging'),

(3, '2024-12-19', '19:00:00', '06:00:00', 11.0, 1, 'excellent', 'Sleeping through the night now!'),
(3, '2024-12-20', '19:15:00', '06:15:00', 11.0, 1, 'excellent', 'Consistent sleep schedule established');
