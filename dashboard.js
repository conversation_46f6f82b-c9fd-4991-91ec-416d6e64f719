// Global variables
let currentUser = null;
let userNotes = [];
let userActivities = {
    feeding: [],
    sleep: [],
    diaper: [],
    play: []
};
let milestoneData = {};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    currentUser = JSON.parse(localStorage.getItem('currentUser'));
    
    if (!currentUser) {
        // Redirect to main page if not logged in
        window.location.href = 'index.html';
        return;
    }
    
    // Load user data
    loadUserData();
    updateUserInterface();
    loadNotes();
    loadActivities();
    loadMilestones();
    calculateBabyAge();
    updateStats();
});

// Load user data and update interface
function updateUserInterface() {
    // Update user info in header and sidebar
    document.getElementById('welcomeUser').textContent = `Welcome, ${currentUser.name}!`;
    document.getElementById('userName').textContent = currentUser.name;
    document.getElementById('userEmail').textContent = currentUser.email;
    
    // Update user initial
    const initial = currentUser.name.charAt(0).toUpperCase();
    document.getElementById('userInitial').textContent = initial;
    
    // Update profile form
    document.getElementById('profileName').value = currentUser.name || '';
    document.getElementById('profileEmail').value = currentUser.email || '';
    document.getElementById('babyName').value = currentUser.babyName || '';
    document.getElementById('babyBirthdate').value = currentUser.babyBirthdate || '';
}

// Navigation functions
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionName).classList.add('active');
    
    // Add active class to clicked nav item
    event.target.classList.add('active');
}

// Baby age calculation
function calculateBabyAge() {
    if (!currentUser.babyBirthdate) {
        document.getElementById('babyAge').textContent = 'Set birthdate in profile';
        return;
    }
    
    const birthDate = new Date(currentUser.babyBirthdate);
    const today = new Date();
    const diffTime = Math.abs(today - birthDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    let ageText;
    if (diffDays < 30) {
        ageText = `${diffDays} days old`;
    } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        const days = diffDays % 30;
        ageText = `${months}m ${days}d old`;
    } else {
        const years = Math.floor(diffDays / 365);
        const months = Math.floor((diffDays % 365) / 30);
        ageText = `${years}y ${months}m old`;
    }
    
    document.getElementById('babyAge').textContent = ageText;
}

// Activity tracking
function logActivity(type) {
    const now = new Date();
    const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    // Add to activities array
    userActivities[type].push({
        time: now,
        timeString: timeString,
        date: now.toDateString()
    });
    
    // Update last activity display
    document.getElementById(`last${type.charAt(0).toUpperCase() + type.slice(1)}`).textContent = timeString;
    
    // Update today's count
    updateTodayCount(type);
    
    // Add to activity history
    addToActivityHistory(`${type.charAt(0).toUpperCase() + type.slice(1)} logged at ${timeString}`);
    
    // Save to localStorage
    saveActivities();
    updateStats();
    
    showMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} logged successfully!`, 'success');
}

function updateTodayCount(type) {
    const today = new Date().toDateString();
    const todayActivities = userActivities[type].filter(activity => 
        activity.date === today
    ).length;
    
    if (type === 'sleep') {
        document.getElementById(`today${type.charAt(0).toUpperCase() + type.slice(1)}`).textContent = `${todayActivities * 2} hours`;
    } else {
        document.getElementById(`today${type.charAt(0).toUpperCase() + type.slice(1)}`).textContent = `${todayActivities} times`;
    }
}

function addToActivityHistory(activity) {
    const historyList = document.getElementById('historyList');
    const activityList = document.getElementById('activityList');
    
    const activityItem = document.createElement('div');
    activityItem.className = 'activity-item';
    activityItem.textContent = `${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${activity}`;
    
    // Add to both history and recent activities
    if (historyList.querySelector('p')) {
        historyList.innerHTML = '';
    }
    if (activityList.querySelector('p')) {
        activityList.innerHTML = '';
    }
    
    historyList.insertBefore(activityItem.cloneNode(true), historyList.firstChild);
    activityList.insertBefore(activityItem, activityList.firstChild);
    
    // Keep only last 10 activities
    while (historyList.children.length > 10) {
        historyList.removeChild(historyList.lastChild);
    }
    while (activityList.children.length > 10) {
        activityList.removeChild(activityList.lastChild);
    }
}

// Milestone tracking
function updateProgress() {
    const checkboxes = document.querySelectorAll('#milestones input[type="checkbox"]');
    const checkedBoxes = document.querySelectorAll('#milestones input[type="checkbox"]:checked');
    
    const percentage = Math.round((checkedBoxes.length / checkboxes.length) * 100);
    
    document.getElementById('progressBar').style.width = `${percentage}%`;
    document.getElementById('progressText').textContent = `${percentage}% Complete`;
    document.getElementById('milestoneProgress').textContent = `${percentage}% Complete`;
    
    // Save milestone progress
    saveMilestones();
    
    if (event && event.target.checked) {
        const label = event.target.nextElementSibling.textContent;
        addToActivityHistory(`Milestone achieved: ${label}`);
        showMessage('Milestone completed!', 'success');
    }
}

// Notes management
function addNote() {
    const noteInput = document.getElementById('noteInput');
    const noteText = noteInput.value.trim();
    
    if (!noteText) {
        showMessage('Please enter a note', 'error');
        return;
    }
    
    const note = {
        id: Date.now(),
        content: noteText,
        date: new Date().toLocaleDateString(),
        time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    };
    
    userNotes.unshift(note);
    noteInput.value = '';
    
    renderNotes();
    saveNotes();
    addToActivityHistory(`Added note: ${noteText.substring(0, 30)}...`);
    showMessage('Note added successfully!', 'success');
}

function deleteNote(noteId) {
    userNotes = userNotes.filter(note => note.id !== noteId);
    renderNotes();
    saveNotes();
    showMessage('Note deleted successfully!', 'success');
}

function renderNotes() {
    const notesList = document.getElementById('notesList');
    
    if (userNotes.length === 0) {
        notesList.innerHTML = '<p>No notes yet. Add your first note above!</p>';
        return;
    }
    
    notesList.innerHTML = userNotes.map(note => `
        <div class="note-item">
            <div class="note-date">${note.date} at ${note.time}</div>
            <div class="note-content">${note.content}</div>
            <button class="note-delete" onclick="deleteNote(${note.id})">Delete</button>
        </div>
    `).join('');
}

// Profile management
function updateProfile(event) {
    event.preventDefault();
    
    const name = document.getElementById('profileName').value;
    const email = document.getElementById('profileEmail').value;
    const babyName = document.getElementById('babyName').value;
    const babyBirthdate = document.getElementById('babyBirthdate').value;
    
    if (!name || !email) {
        showMessage('Name and email are required', 'error');
        return;
    }
    
    // Update current user
    currentUser.name = name;
    currentUser.email = email;
    currentUser.babyName = babyName;
    currentUser.babyBirthdate = babyBirthdate;
    
    // Update in localStorage
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    
    // Update users array
    let users = JSON.parse(localStorage.getItem('users')) || [];
    const userIndex = users.findIndex(user => user.email === currentUser.email);
    if (userIndex !== -1) {
        users[userIndex] = currentUser;
        localStorage.setItem('users', JSON.stringify(users));
    }
    
    updateUserInterface();
    calculateBabyAge();
    
    showMessage('Profile updated successfully!', 'success');
}

// Update stats
function updateStats() {
    const today = new Date().toDateString();
    
    // Update feeding count
    const todayFeedings = userActivities.feeding.filter(activity => 
        activity.date === today
    ).length;
    document.getElementById('feedingCount').textContent = todayFeedings;
    
    // Update sleep hours
    const todaySleep = userActivities.sleep.filter(activity => 
        activity.date === today
    ).length;
    document.getElementById('sleepHours').textContent = `${todaySleep * 2}`;
}

// Data persistence functions
function loadUserData() {
    const userKey = `userData_${currentUser.email}`;
    const userData = JSON.parse(localStorage.getItem(userKey));
    
    if (userData) {
        userNotes = userData.notes || [];
        userActivities = userData.activities || { feeding: [], sleep: [], diaper: [], play: [] };
        milestoneData = userData.milestones || {};
    }
}

function saveNotes() {
    const userKey = `userData_${currentUser.email}`;
    const userData = JSON.parse(localStorage.getItem(userKey)) || {};
    userData.notes = userNotes;
    localStorage.setItem(userKey, JSON.stringify(userData));
}

function saveActivities() {
    const userKey = `userData_${currentUser.email}`;
    const userData = JSON.parse(localStorage.getItem(userKey)) || {};
    userData.activities = userActivities;
    localStorage.setItem(userKey, JSON.stringify(userData));
}

function saveMilestones() {
    const checkboxes = document.querySelectorAll('#milestones input[type="checkbox"]');
    milestoneData = {};
    
    checkboxes.forEach(checkbox => {
        milestoneData[checkbox.id] = checkbox.checked;
    });
    
    const userKey = `userData_${currentUser.email}`;
    const userData = JSON.parse(localStorage.getItem(userKey)) || {};
    userData.milestones = milestoneData;
    localStorage.setItem(userKey, JSON.stringify(userData));
}

function loadMilestones() {
    Object.keys(milestoneData).forEach(checkboxId => {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
            checkbox.checked = milestoneData[checkboxId];
        }
    });
    updateProgress();
}

function loadActivities() {
    // Convert time strings back to Date objects
    Object.keys(userActivities).forEach(key => {
        userActivities[key] = userActivities[key].map(activity => ({
            ...activity,
            time: new Date(activity.time)
        }));
    });
    
    // Update last activity displays
    Object.keys(userActivities).forEach(type => {
        if (userActivities[type].length > 0) {
            const lastActivity = userActivities[type][userActivities[type].length - 1];
            document.getElementById(`last${type.charAt(0).toUpperCase() + type.slice(1)}`).textContent = lastActivity.timeString;
            updateTodayCount(type);
        }
    });
}

// Account deletion
function deleteAccount() {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
        // Remove user from users array
        let users = JSON.parse(localStorage.getItem('users')) || [];
        users = users.filter(user => user.email !== currentUser.email);
        localStorage.setItem('users', JSON.stringify(users));
        
        // Remove user data
        const userKey = `userData_${currentUser.email}`;
        localStorage.removeItem(userKey);
        
        // Clear current user
        localStorage.removeItem('currentUser');
        
        showMessage('Account deleted successfully', 'success');
        
        // Redirect to main page after 2 seconds
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Logout function
function logout() {
    localStorage.removeItem('currentUser');
    showMessage('Logged out successfully!', 'success');
    
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);
}

// Utility function for messages
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}
