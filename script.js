// Global variables for user data
let currentUser = null;
let userNotes = [];
let babyActivities = {
    feeding: [],
    sleep: [],
    diaper: [],
    play: []
};

// Smooth scrolling function
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Authentication Functions
function showLoginModal() {
    document.getElementById('loginModal').style.display = 'block';
}

function showSignupModal() {
    document.getElementById('signupModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function switchToSignup() {
    closeModal('loginModal');
    showSignupModal();
}

function switchToLogin() {
    closeModal('signupModal');
    showLoginModal();
}

function handleLogin(event) {
    event.preventDefault();
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    // Simple validation (in real app, this would be server-side)
    if (email && password) {
        // Simulate successful login
        currentUser = {
            name: email.split('@')[0],
            email: email,
            babyName: 'Baby',
            babyBirthdate: null
        };

        loginUser();
        closeModal('loginModal');
        showSuccessMessage('Login successful!');
    } else {
        showErrorMessage('Please fill in all fields');
    }
}

function handleSignup(event) {
    event.preventDefault();
    const name = document.getElementById('signupName').value;
    const email = document.getElementById('signupEmail').value;
    const password = document.getElementById('signupPassword').value;
    const babyName = document.getElementById('babyName').value || 'Baby';
    const babyBirthdate = document.getElementById('babyBirthdate').value;

    if (name && email && password) {
        // Simulate successful signup
        currentUser = {
            name: name,
            email: email,
            babyName: babyName,
            babyBirthdate: babyBirthdate
        };

        loginUser();
        closeModal('signupModal');
        showSuccessMessage('Account created successfully!');
    } else {
        showErrorMessage('Please fill in all required fields');
    }
}

function loginUser() {
    // Hide auth buttons, show user menu
    document.querySelector('.login-btn').style.display = 'none';
    document.querySelector('.signup-btn').style.display = 'none';
    document.getElementById('userMenu').style.display = 'block';

    // Update user avatar
    const userInitial = currentUser.name.charAt(0).toUpperCase();
    document.getElementById('userInitial').textContent = userInitial;

    // Update dashboard info
    updateDashboardUserInfo();
}

function logout() {
    currentUser = null;
    userNotes = [];
    babyActivities = { feeding: [], sleep: [], diaper: [], play: [] };

    // Show auth buttons, hide user menu
    document.querySelector('.login-btn').style.display = 'inline-block';
    document.querySelector('.signup-btn').style.display = 'inline-block';
    document.getElementById('userMenu').style.display = 'none';

    // Hide dropdown
    document.getElementById('userDropdown').classList.remove('show');

    // Close dashboard if open
    closeDashboard();

    showSuccessMessage('Logged out successfully!');
}

function toggleUserDropdown() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
}

// Dashboard Functions
function showDashboard() {
    if (!currentUser) {
        showLoginModal();
        return;
    }

    document.getElementById('dashboard').style.display = 'block';
    updateDashboardUserInfo();
    calculateBabyAge();
    updateActivityCounts();
    hideUserDropdown();
}

function closeDashboard() {
    document.getElementById('dashboard').style.display = 'none';
}

function hideUserDropdown() {
    document.getElementById('userDropdown').classList.remove('show');
}

function showProfile() {
    showDashboard();
    showDashboardSection('profile');
    hideUserDropdown();
}

function updateDashboardUserInfo() {
    if (!currentUser) return;

    const userInitial = currentUser.name.charAt(0).toUpperCase();
    document.getElementById('dashboardUserInitial').textContent = userInitial;
    document.getElementById('dashboardUserName').textContent = currentUser.name;
    document.getElementById('dashboardUserEmail').textContent = currentUser.email;

    // Update profile form
    document.getElementById('profileName').value = currentUser.name;
    document.getElementById('profileEmail').value = currentUser.email;
    document.getElementById('profileBabyName').value = currentUser.babyName;
    document.getElementById('profileBabyBirthdate').value = currentUser.babyBirthdate || '';
}

function showDashboardSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.dashboard-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from nav items
    document.querySelectorAll('.dashboard-nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionName + '-section').classList.add('active');

    // Add active class to clicked nav item
    event.target.classList.add('active');
}

// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        const loginModal = document.getElementById('loginModal');
        const signupModal = document.getElementById('signupModal');

        if (event.target === loginModal) {
            closeModal('loginModal');
        }
        if (event.target === signupModal) {
            closeModal('signupModal');
        }
    });

    // Close user dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const userMenu = document.querySelector('.user-menu');
        const dropdown = document.getElementById('userDropdown');

        if (!userMenu.contains(event.target)) {
            dropdown.classList.remove('show');
        }
    });

    // Load saved notes from localStorage
    loadNotes();
    loadActivities();
});

// Baby Age Calculation
function calculateBabyAge() {
    if (!currentUser || !currentUser.babyBirthdate) {
        document.getElementById('babyAge').textContent = 'Set birthdate';
        return;
    }

    const birthDate = new Date(currentUser.babyBirthdate);
    const today = new Date();
    const diffTime = Math.abs(today - birthDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 30) {
        document.getElementById('babyAge').textContent = `${diffDays} days old`;
    } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        const days = diffDays % 30;
        document.getElementById('babyAge').textContent = `${months}m ${days}d old`;
    } else {
        const years = Math.floor(diffDays / 365);
        const months = Math.floor((diffDays % 365) / 30);
        document.getElementById('babyAge').textContent = `${years}y ${months}m old`;
    }
}

// Activity Tracking
function logActivity(type) {
    const now = new Date();
    const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    babyActivities[type].push({
        time: now,
        timeString: timeString
    });

    // Update last activity display
    document.getElementById(`last${type.charAt(0).toUpperCase() + type.slice(1)}`).textContent = timeString;

    // Update activity counts
    updateActivityCounts();

    // Add to recent activities
    addToRecentActivities(`Logged ${type} at ${timeString}`);

    // Save to localStorage
    saveActivities();

    showSuccessMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} logged successfully!`);
}

function updateActivityCounts() {
    const today = new Date().toDateString();

    // Count today's feedings
    const todayFeedings = babyActivities.feeding.filter(activity =>
        activity.time.toDateString() === today
    ).length;
    document.getElementById('feedingCount').textContent = `${todayFeedings} times`;

    // Calculate sleep hours (simplified)
    const todaySleep = babyActivities.sleep.filter(activity =>
        activity.time.toDateString() === today
    ).length;
    document.getElementById('sleepHours').textContent = `${todaySleep * 2} hours`;
}

// Milestone Tracking
function updateMilestone(checkbox) {
    const checkedBoxes = document.querySelectorAll('#milestones-section input[type="checkbox"]:checked').length;
    const totalBoxes = document.querySelectorAll('#milestones-section input[type="checkbox"]').length;
    const percentage = Math.round((checkedBoxes / totalBoxes) * 100);

    document.getElementById('milestoneProgress').style.width = `${percentage}%`;
    document.querySelector('.milestone-progress p').textContent = `${percentage}% Complete`;

    if (checkbox.checked) {
        addToRecentActivities(`Milestone achieved: ${checkbox.nextElementSibling.textContent}`);
    }
}

// Notes Management
function addNote() {
    const noteInput = document.getElementById('noteInput');
    const noteText = noteInput.value.trim();

    if (!noteText) {
        showErrorMessage('Please enter a note');
        return;
    }

    const note = {
        id: Date.now(),
        content: noteText,
        date: new Date().toLocaleDateString(),
        time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})
    };

    userNotes.unshift(note);
    noteInput.value = '';

    renderNotes();
    saveNotes();
    addToRecentActivities(`Added note: ${noteText.substring(0, 30)}...`);
    showSuccessMessage('Note added successfully!');
}

function deleteNote(noteId) {
    userNotes = userNotes.filter(note => note.id !== noteId);
    renderNotes();
    saveNotes();
    showSuccessMessage('Note deleted successfully!');
}

function renderNotes() {
    const notesList = document.getElementById('notesList');

    if (userNotes.length === 0) {
        notesList.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">No notes yet. Add your first note above!</p>';
        return;
    }

    notesList.innerHTML = userNotes.map(note => `
        <div class="note-item">
            <div class="note-date">${note.date} at ${note.time}</div>
            <div class="note-content">${note.content}</div>
            <button class="note-delete" onclick="deleteNote(${note.id})">Delete</button>
        </div>
    `).join('');
}

// Profile Management
function updateProfile(event) {
    event.preventDefault();

    const name = document.getElementById('profileName').value;
    const email = document.getElementById('profileEmail').value;
    const babyName = document.getElementById('profileBabyName').value;
    const babyBirthdate = document.getElementById('profileBabyBirthdate').value;

    if (!name || !email) {
        showErrorMessage('Name and email are required');
        return;
    }

    currentUser.name = name;
    currentUser.email = email;
    currentUser.babyName = babyName;
    currentUser.babyBirthdate = babyBirthdate;

    updateDashboardUserInfo();
    calculateBabyAge();

    showSuccessMessage('Profile updated successfully!');
}

// Recent Activities
function addToRecentActivities(activity) {
    const activityList = document.getElementById('activityList');
    const li = document.createElement('li');
    li.textContent = `${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${activity}`;

    activityList.insertBefore(li, activityList.firstChild);

    // Keep only last 10 activities
    while (activityList.children.length > 10) {
        activityList.removeChild(activityList.lastChild);
    }
}

// Local Storage Functions
function saveNotes() {
    localStorage.setItem('babyNotes', JSON.stringify(userNotes));
}

function loadNotes() {
    const saved = localStorage.getItem('babyNotes');
    if (saved) {
        userNotes = JSON.parse(saved);
        renderNotes();
    }
}

function saveActivities() {
    localStorage.setItem('babyActivities', JSON.stringify(babyActivities));
}

function loadActivities() {
    const saved = localStorage.getItem('babyActivities');
    if (saved) {
        const parsed = JSON.parse(saved);
        // Convert time strings back to Date objects
        Object.keys(parsed).forEach(key => {
            parsed[key] = parsed[key].map(activity => ({
                ...activity,
                time: new Date(activity.time)
            }));
        });
        babyActivities = parsed;
    }
}

// Utility Functions
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showErrorMessage(message) {
    showMessage(message, 'error');
}

function showMessage(message, type) {
    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 3000;
        animation: slideIn 0.3s ease;
        background: ${type === 'success' ? 'linear-gradient(135deg, #4caf50, #45a049)' : 'linear-gradient(135deg, #f44336, #d32f2f)'};
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(messageDiv);

    // Remove after 3 seconds
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// Add CSS for message animations
const messageStyle = document.createElement('style');
messageStyle.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(messageStyle);

// Initialize page animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Milestone cards animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe milestone cards
    const milestoneCards = document.querySelectorAll('.milestone-card');
    milestoneCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Observe tip cards
    const tipCards = document.querySelectorAll('.tip-card');
    tipCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Observe resource cards
    const resourceCards = document.querySelectorAll('.resource-card');
    resourceCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Add click interaction to milestone cards
    milestoneCards.forEach(card => {
        card.addEventListener('click', function() {
            const month = this.getAttribute('data-month');
            showMilestoneDetails(month);
        });
    });

    // Header background change on scroll
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(102, 126, 234, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            header.style.backdropFilter = 'none';
        }
    });

    // Add click effect to CTA button
    const ctaButton = document.querySelector('.cta-button');
    if (ctaButton) {
        ctaButton.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    }

    // Add hover effect to baby illustration
    const babyIllustration = document.querySelector('.illustration-circle');
    if (babyIllustration) {
        babyIllustration.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });

        babyIllustration.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    }
});

// Show milestone details function
function showMilestoneDetails(month) {
    const milestoneDetails = {
        '0-3': {
            title: '0-3 Months: The Fourth Trimester',
            details: [
                'Your baby is adjusting to life outside the womb',
                'Sleep patterns are irregular - this is normal',
                'Feeding every 2-3 hours is typical',
                'Tummy time helps strengthen neck and shoulder muscles',
                'Baby may sleep 14-17 hours per day in short bursts'
            ]
        },
        '4-6': {
            title: '4-6 Months: Social Butterfly Emerges',
            details: [
                'Baby becomes more social and interactive',
                'May start showing interest in solid foods around 6 months',
                'Sleep patterns may begin to regulate',
                'Hand-eye coordination improves significantly',
                'May begin to show stranger awareness'
            ]
        },
        '7-9': {
            title: '7-9 Months: Mobile and Curious',
            details: [
                'Mobility increases - baby-proof your home',
                'Separation anxiety may begin',
                'Pincer grasp develops (thumb and forefinger)',
                'May start to understand simple words like "no"',
                'Enjoys playing with cause-and-effect toys'
            ]
        },
        '10-12': {
            title: '10-12 Months: Almost a Toddler',
            details: [
                'Personality really starts to shine through',
                'May take first independent steps',
                'Understands simple commands',
                'Enjoys interactive games and songs',
                'May start to assert independence'
            ]
        }
    };

    const details = milestoneDetails[month];
    if (details) {
        showMessage(`${details.title}\n\n• ${details.details.join('\n• ')}`, 'info');
    }
}
