/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.logo {
    font-size: 1.6rem;
    font-weight: 700;
    color: #fff;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

/* Authentication Section */
.auth-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.login-btn, .signup-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.login-btn {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.login-btn:hover {
    background: #fff;
    color: #667eea;
}

.signup-btn {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: #fff;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
}

.user-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    padding: 10px 0;
    min-width: 150px;
    display: none;
}

.user-dropdown.show {
    display: block;
}

.user-dropdown a {
    display: block;
    padding: 10px 20px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.user-dropdown a:hover {
    background: #f5f5f5;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #fff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 140px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.8;
}

.cta-button {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.cta-button:hover {
    background: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.hero-image {
    text-align: center;
}

.hero-img {
    width: 350px;
    height: 350px;
    border-radius: 15px;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Milestones Section */
.milestones {
    padding: 80px 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.section-description {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.milestone-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.milestone-card:hover {
    transform: translateY(-5px);
}

.milestone-img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.milestone-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.milestone-card ul {
    list-style: none;
    text-align: left;
}

.milestone-card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.milestone-card li:last-child {
    border-bottom: none;
}

.milestone-card li:before {
    content: "✓ ";
    color: #4caf50;
    font-weight: bold;
}

/* Tips Section */
.tips {
    padding: 80px 0;
    background: white;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tip-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
}

.tip-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tip-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.tip-card p {
    color: #555;
    line-height: 1.6;
}

/* Resources Section */
.resources {
    padding: 80px 0;
    background: #f8f9fa;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.resource-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.resource-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.resource-card ul {
    list-style: none;
}

.resource-card li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.resource-card li:last-child {
    border-bottom: none;
}

.resource-card li:before {
    content: "• ";
    color: #ff6b6b;
    font-weight: bold;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

.footer p {
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        height: 70px;
    }

    .logo {
        font-size: 1.2rem;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .auth-section {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(102, 126, 234, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        backdrop-filter: blur(10px);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-img {
        width: 280px;
        height: 280px;
    }

    .milestone-img {
        height: 140px;
    }

    .tip-img {
        height: 120px;
    }

    .timeline {
        grid-template-columns: 1fr;
    }

    .tips-grid {
        grid-template-columns: 1fr;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        flex-direction: column;
        height: auto;
    }

    .dashboard-sidebar {
        width: 100%;
        padding: 20px 0;
    }

    .dashboard-main {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .tracker-grid {
        grid-template-columns: 1fr;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #000;
}

.modal h2 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.form-btn:hover {
    transform: translateY(-2px);
}

.form-switch {
    text-align: center;
    margin-top: 15px;
    color: #666;
}

.form-switch a {
    color: #667eea;
    text-decoration: none;
}

.form-switch a:hover {
    text-decoration: underline;
}

/* Dashboard Styles */
.dashboard {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    z-index: 1500;
    overflow: hidden;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dashboard-header h2 {
    margin: 0;
    font-size: 1.8rem;
}

.close-dashboard {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.close-dashboard:hover {
    background: rgba(255,255,255,0.1);
}

.dashboard-content {
    display: flex;
    height: calc(100vh - 80px);
}

.dashboard-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 2px 0 20px rgba(0,0,0,0.1);
    padding: 30px 0;
    border-right: 1px solid rgba(102, 126, 234, 0.1);
}

.user-info {
    text-align: center;
    padding: 0 30px 30px;
    border-bottom: 1px solid #e1e1e1;
    margin-bottom: 30px;
}

.user-avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    margin: 0 auto 15px;
}

.user-info h3 {
    margin: 0 0 5px;
    color: #333;
}

.user-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.dashboard-nav {
    padding: 0 15px;
}

.dashboard-nav-item {
    display: block;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    border-radius: 10px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.dashboard-nav-item:hover,
.dashboard-nav-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.dashboard-main {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.dashboard-section h3 {
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.stat-card h4 {
    margin: 0 0 10px;
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-card p {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.recent-activities {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.recent-activities h4 {
    margin: 0 0 20px;
    color: #333;
}

.recent-activities ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recent-activities li {
    padding: 10px 0;
    border-bottom: 1px solid #e1e1e1;
    color: #666;
}

.recent-activities li:last-child {
    border-bottom: none;
}

/* Milestone Tracker */
.milestone-tracker {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.milestone-progress {
    margin-bottom: 30px;
}

.milestone-progress h4 {
    margin: 0 0 15px;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.checklist-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e1e1e1;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-item input[type="checkbox"] {
    margin-right: 15px;
    transform: scale(1.2);
}

.checklist-item label {
    color: #333;
    cursor: pointer;
}

/* Baby Tracker */
.tracker-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tracker-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.tracker-card h4 {
    margin: 0 0 20px;
    color: #333;
}

.tracker-btn {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin-bottom: 15px;
}

.tracker-btn:hover {
    transform: translateY(-2px);
}

.tracker-card p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Notes */
.notes-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.add-note {
    margin-bottom: 30px;
}

.add-note textarea {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 10px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    margin-bottom: 15px;
}

.add-note textarea:focus {
    outline: none;
    border-color: #667eea;
}

.add-note-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.add-note-btn:hover {
    transform: translateY(-2px);
}

.note-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.note-item:last-child {
    margin-bottom: 0;
}

.note-date {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 10px;
}

.note-content {
    color: #333;
    line-height: 1.6;
}

.note-delete {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    float: right;
    margin-top: 10px;
}

.note-delete:hover {
    background: #ff5252;
}
