/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logout-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 90px);
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 20px rgba(0,0,0,0.1);
    padding: 30px 0;
}

.user-profile {
    text-align: center;
    padding: 0 30px 30px;
    border-bottom: 1px solid #e1e1e1;
    margin-bottom: 30px;
}

.user-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 2rem;
    margin: 0 auto 15px;
}

.user-profile h3 {
    margin: 0 0 5px;
    color: #333;
    font-size: 1.2rem;
}

.user-profile p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.sidebar-nav {
    padding: 0 15px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    border-radius: 10px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-item:hover,
.nav-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.nav-icon {
    font-size: 1.2rem;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.content-section h2 {
    margin-bottom: 30px;
    color: #333;
    font-size: 2rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    margin: 0 0 10px;
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-card p {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

/* Recent Activities */
.recent-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.recent-section h3 {
    margin: 0 0 20px;
    color: #333;
}

.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px 0;
    border-bottom: 1px solid #e1e1e1;
    color: #666;
}

.activity-item:last-child {
    border-bottom: none;
}

/* Milestone Progress */
.milestone-progress {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.milestone-progress h3 {
    margin: 0 0 15px;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e1e1e1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

/* Milestone Categories */
.milestone-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.milestone-category {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.milestone-category h4 {
    margin: 0 0 20px;
    color: #667eea;
    font-size: 1.2rem;
}

.milestone-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e1e1e1;
}

.milestone-item:last-child {
    border-bottom: none;
}

.milestone-item input[type="checkbox"] {
    margin-right: 15px;
    transform: scale(1.2);
}

.milestone-item label {
    color: #333;
    cursor: pointer;
    flex: 1;
}

/* Tracker Grid */
.tracker-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tracker-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
}

.tracker-card h3 {
    margin: 0 0 20px;
    color: #333;
}

.track-btn {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin-bottom: 15px;
}

.track-btn:hover {
    transform: translateY(-2px);
}

.tracker-card p {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

/* Activity History */
.activity-history {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.activity-history h3 {
    margin: 0 0 20px;
    color: #333;
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

/* Notes Section */
.add-note-section {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.add-note-section textarea {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 10px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    margin-bottom: 15px;
}

.add-note-section textarea:focus {
    outline: none;
    border-color: #667eea;
}

.add-note-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.add-note-btn:hover {
    transform: translateY(-2px);
}

.notes-list {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.note-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.note-item:last-child {
    margin-bottom: 0;
}

.note-date {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 10px;
}

.note-content {
    color: #333;
    line-height: 1.6;
}

.note-delete {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    float: right;
    margin-top: 10px;
}

/* Profile Form */
#profileForm {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    padding: 12px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.save-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.save-btn:hover {
    transform: translateY(-2px);
}

/* Danger Zone */
.danger-zone {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-left: 4px solid #ff6b6b;
}

.danger-zone h3 {
    margin: 0 0 15px;
    color: #ff6b6b;
}

.delete-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.delete-btn:hover {
    background: #ff5252;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 20px 0;
    }

    .main-content {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .tracker-grid {
        grid-template-columns: 1fr;
    }

    .milestone-categories {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Success/Error Messages */
.message {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 3000;
    animation: slideIn 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.message.success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.message.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
