<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>From Cradle to Crawl: A Guide to Baby's First Year</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="logo-container">
                    <div class="logo-icon">
                        <span>B</span>
                    </div>
                    <h1 class="logo">Baby's First Year</h1>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">Home</a></li>
                    <li><a href="#milestones" class="nav-link">Milestones</a></li>
                    <li><a href="#tips" class="nav-link">Tips</a></li>
                    <li><a href="#resources" class="nav-link">Resources</a></li>
                </ul>
                <div class="auth-section">
                    <button class="login-btn" onclick="showLoginModal()">Login</button>
                    <button class="signup-btn" onclick="showSignupModal()">Sign Up</button>
                    <div class="user-menu" id="userMenu" style="display: none;">
                        <div class="user-avatar" onclick="toggleUserDropdown()">
                            <span id="userInitial">U</span>
                        </div>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#" onclick="showDashboard()">Dashboard</a>
                            <a href="#" onclick="showProfile()">Profile</a>
                            <a href="#" onclick="logout()">Logout</a>
                        </div>
                    </div>
                </div>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">From Cradle to Crawl</h1>
                <h2 class="hero-subtitle">A Complete Guide to Baby's First Year</h2>
                <p class="hero-description">
                    Navigate the wonderful journey of your baby's first year with confidence.
                    Discover important milestones, helpful tips, and expert guidance for new parents.
                </p>
                <button class="cta-button" onclick="scrollToSection('milestones')">
                    Explore Milestones
                </button>
            </div>
            <div class="hero-image">
                <img src="https://images.unsplash.com/photo-1515488042361-ee00e0ddd4e4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Cute baby" class="hero-img">
            </div>
        </div>
    </section>

    <!-- Milestones Section -->
    <section id="milestones" class="milestones">
        <div class="container">
            <h2 class="section-title">Developmental Milestones</h2>
            <p class="section-description">Track your baby's growth and development month by month</p>

            <div class="timeline">
                <div class="milestone-card" data-month="0-3">
                    <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80" alt="Newborn baby" class="milestone-img">
                    <h3>0-3 Months</h3>
                    <ul>
                        <li>Lifts head when on tummy</li>
                        <li>Follows objects with eyes</li>
                        <li>Begins to smile</li>
                        <li>Makes cooing sounds</li>
                    </ul>
                </div>

                <div class="milestone-card" data-month="4-6">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80" alt="Baby sitting" class="milestone-img">
                    <h3>4-6 Months</h3>
                    <ul>
                        <li>Rolls over</li>
                        <li>Sits with support</li>
                        <li>Reaches for toys</li>
                        <li>Babbles and laughs</li>
                    </ul>
                </div>

                <div class="milestone-card" data-month="7-9">
                    <img src="https://images.unsplash.com/photo-1555252333-9f8e92e65df9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80" alt="Baby crawling" class="milestone-img">
                    <h3>7-9 Months</h3>
                    <ul>
                        <li>Sits without support</li>
                        <li>Crawls or scoots</li>
                        <li>Says "mama" or "dada"</li>
                        <li>Plays peek-a-boo</li>
                    </ul>
                </div>

                <div class="milestone-card" data-month="10-12">
                    <img src="https://images.unsplash.com/photo-1566004100631-35d015d6a491?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80" alt="Baby walking" class="milestone-img">
                    <h3>10-12 Months</h3>
                    <ul>
                        <li>Pulls to standing</li>
                        <li>Takes first steps</li>
                        <li>Says first words</li>
                        <li>Waves goodbye</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Tips Section -->
    <section id="tips" class="tips">
        <div class="container">
            <h2 class="section-title">Essential Parenting Tips</h2>
            <div class="tips-grid">
                <div class="tip-card">
                    <img src="https://images.unsplash.com/photo-1520452112805-c6692c840af0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80" alt="Baby sleeping" class="tip-img">
                    <h3>Sleep</h3>
                    <p>Establish a consistent bedtime routine. Most newborns sleep 14-17 hours per day in short periods.</p>
                </div>

                <div class="tip-card">
                    <img src="images/Feeding baby.jpg" alt="Baby feeding" class="tip-img">
                    <h3>Feeding</h3>
                    <p>Feed on demand during the first few weeks. Breastfed babies eat every 2-3 hours, formula-fed every 3-4 hours.</p>
                </div>

                <div class="tip-card">
                    <img src="images/Bathing.jpeg" alt="Baby bathing" class="tip-img">
                    <h3>Bathing</h3>
                    <p>Newborns only need baths 2-3 times per week. Keep water warm (not hot) and never leave baby alone.</p>
                </div>

                <div class="tip-card">
                    <img src="images/Development.jpg" alt="Baby development" class="tip-img">
                    <h3>Development</h3>
                    <p>Talk, sing, and read to your baby daily. This helps with language development and bonding.</p>
                </div>

                <div class="tip-card">
                    <img src="images/Health.jpeg" alt="Baby health" class="tip-img">
                    <h3>Health</h3>
                    <p>Keep up with regular pediatric checkups and vaccinations. Trust your instincts about your baby's health.</p>
                </div>

                <div class="tip-card">
                    <img src="images/bonding.jpeg" alt="Baby bonding" class="tip-img">
                    <h3>Bonding</h3>
                    <p>Skin-to-skin contact, eye contact during feeding, and responding to baby's cues strengthen your bond.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Resources Section -->
    <section id="resources" class="resources">
        <div class="container">
            <h2 class="section-title">Helpful Resources</h2>
            <div class="resources-grid">
                <div class="resource-card">
                    <h3>Recommended Reading</h3>
                    <ul>
                        <li>"What to Expect the First Year"</li>
                        <li>"The Happiest Baby on the Block"</li>
                        <li>"Baby 411"</li>
                    </ul>
                </div>

                <div class="resource-card">
                    <h3>Useful Apps</h3>
                    <ul>
                        <li>Baby Tracker (feeding & sleep)</li>
                        <li>Wonder Weeks (development)</li>
                        <li>Pediatric checkup reminders</li>
                    </ul>
                </div>

                <div class="resource-card">
                    <h3>When to Call Doctor</h3>
                    <ul>
                        <li>Fever over 100.4°F (38°C)</li>
                        <li>Difficulty breathing</li>
                        <li>Persistent crying</li>
                        <li>Feeding difficulties</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('loginModal')">&times;</span>
            <h2>Login to Your Account</h2>
            <form id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="loginEmail">Email:</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password:</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="form-btn">Login</button>
                <p class="form-switch">Don't have an account? <a href="#" onclick="switchToSignup()">Sign up here</a></p>
            </form>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signupModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('signupModal')">&times;</span>
            <h2>Create Your Account</h2>
            <form id="signupForm" onsubmit="handleSignup(event)">
                <div class="form-group">
                    <label for="signupName">Full Name:</label>
                    <input type="text" id="signupName" required>
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email:</label>
                    <input type="email" id="signupEmail" required>
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password:</label>
                    <input type="password" id="signupPassword" required>
                </div>
                <div class="form-group">
                    <label for="babyName">Baby's Name (Optional):</label>
                    <input type="text" id="babyName">
                </div>
                <div class="form-group">
                    <label for="babyBirthdate">Baby's Birthdate (Optional):</label>
                    <input type="date" id="babyBirthdate">
                </div>
                <button type="submit" class="form-btn">Sign Up</button>
                <p class="form-switch">Already have an account? <a href="#" onclick="switchToLogin()">Login here</a></p>
            </form>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="dashboard" style="display: none;">
        <div class="dashboard-header">
            <h2>Welcome to Your Dashboard</h2>
            <button class="close-dashboard" onclick="closeDashboard()">&times;</button>
        </div>
        <div class="dashboard-content">
            <div class="dashboard-sidebar">
                <div class="user-info">
                    <div class="user-avatar-large">
                        <span id="dashboardUserInitial">U</span>
                    </div>
                    <h3 id="dashboardUserName">User Name</h3>
                    <p id="dashboardUserEmail"><EMAIL></p>
                </div>
                <nav class="dashboard-nav">
                    <a href="#" class="dashboard-nav-item active" onclick="showDashboardSection('overview')">Overview</a>
                    <a href="#" class="dashboard-nav-item" onclick="showDashboardSection('milestones')">Milestones</a>
                    <a href="#" class="dashboard-nav-item" onclick="showDashboardSection('tracker')">Baby Tracker</a>
                    <a href="#" class="dashboard-nav-item" onclick="showDashboardSection('notes')">Notes</a>
                    <a href="#" class="dashboard-nav-item" onclick="showDashboardSection('profile')">Profile</a>
                </nav>
            </div>
            <div class="dashboard-main">
                <!-- Overview Section -->
                <div id="overview-section" class="dashboard-section active">
                    <h3>Overview</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h4>Baby's Age</h4>
                            <p id="babyAge">Calculate age</p>
                        </div>
                        <div class="stat-card">
                            <h4>Next Milestone</h4>
                            <p id="nextMilestone">Rolling over</p>
                        </div>
                        <div class="stat-card">
                            <h4>Feeding Today</h4>
                            <p id="feedingCount">0 times</p>
                        </div>
                        <div class="stat-card">
                            <h4>Sleep Hours</h4>
                            <p id="sleepHours">0 hours</p>
                        </div>
                    </div>
                    <div class="recent-activities">
                        <h4>Recent Activities</h4>
                        <ul id="activityList">
                            <li>Welcome to Baby's First Year!</li>
                        </ul>
                    </div>
                </div>

                <!-- Milestones Section -->
                <div id="milestones-section" class="dashboard-section">
                    <h3>Milestone Tracker</h3>
                    <div class="milestone-tracker">
                        <div class="milestone-progress">
                            <h4>Current Progress</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" id="milestoneProgress" style="width: 25%"></div>
                            </div>
                            <p>25% Complete</p>
                        </div>
                        <div class="milestone-checklist">
                            <h4>Upcoming Milestones</h4>
                            <div class="checklist-item">
                                <input type="checkbox" id="milestone1" onchange="updateMilestone(this)">
                                <label for="milestone1">Lifts head when on tummy</label>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" id="milestone2" onchange="updateMilestone(this)">
                                <label for="milestone2">Follows objects with eyes</label>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" id="milestone3" onchange="updateMilestone(this)">
                                <label for="milestone3">Begins to smile</label>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" id="milestone4" onchange="updateMilestone(this)">
                                <label for="milestone4">Makes cooing sounds</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Baby Tracker Section -->
                <div id="tracker-section" class="dashboard-section">
                    <h3>Baby Tracker</h3>
                    <div class="tracker-grid">
                        <div class="tracker-card">
                            <h4>Feeding</h4>
                            <button class="tracker-btn" onclick="logActivity('feeding')">Log Feeding</button>
                            <p>Last: <span id="lastFeeding">Not recorded</span></p>
                        </div>
                        <div class="tracker-card">
                            <h4>Sleep</h4>
                            <button class="tracker-btn" onclick="logActivity('sleep')">Log Sleep</button>
                            <p>Last: <span id="lastSleep">Not recorded</span></p>
                        </div>
                        <div class="tracker-card">
                            <h4>Diaper Change</h4>
                            <button class="tracker-btn" onclick="logActivity('diaper')">Log Change</button>
                            <p>Last: <span id="lastDiaper">Not recorded</span></p>
                        </div>
                        <div class="tracker-card">
                            <h4>Playtime</h4>
                            <button class="tracker-btn" onclick="logActivity('play')">Log Playtime</button>
                            <p>Last: <span id="lastPlay">Not recorded</span></p>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                <div id="notes-section" class="dashboard-section">
                    <h3>My Notes</h3>
                    <div class="notes-container">
                        <div class="add-note">
                            <textarea id="noteInput" placeholder="Add a new note about your baby..."></textarea>
                            <button onclick="addNote()" class="add-note-btn">Add Note</button>
                        </div>
                        <div class="notes-list" id="notesList">
                            <!-- Notes will be added here -->
                        </div>
                    </div>
                </div>

                <!-- Profile Section -->
                <div id="profile-section" class="dashboard-section">
                    <h3>Profile Settings</h3>
                    <form id="profileForm" onsubmit="updateProfile(event)">
                        <div class="form-group">
                            <label for="profileName">Your Name:</label>
                            <input type="text" id="profileName">
                        </div>
                        <div class="form-group">
                            <label for="profileEmail">Email:</label>
                            <input type="email" id="profileEmail">
                        </div>
                        <div class="form-group">
                            <label for="profileBabyName">Baby's Name:</label>
                            <input type="text" id="profileBabyName">
                        </div>
                        <div class="form-group">
                            <label for="profileBabyBirthdate">Baby's Birthdate:</label>
                            <input type="date" id="profileBabyBirthdate">
                        </div>
                        <button type="submit" class="form-btn">Update Profile</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 From Cradle to Crawl. A guide for loving parents.</p>
            <p><small>Always consult with your pediatrician for personalized advice.</small></p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
