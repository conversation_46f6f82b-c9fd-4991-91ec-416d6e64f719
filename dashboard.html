<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Baby's First Year</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Dashboard Header -->
    <header class="dashboard-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-icon">
                    <span>B</span>
                </div>
                <h1>Baby's First Year Dashboard</h1>
            </div>
            <div class="user-section">
                <div class="user-info">
                    <span id="welcomeUser">Welcome, User!</span>
                </div>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>
        </div>
    </header>

    <!-- Dashboard Content -->
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="user-profile">
                <div class="user-avatar">
                    <span id="userInitial">U</span>
                </div>
                <h3 id="userName">User Name</h3>
                <p id="userEmail"><EMAIL></p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="#" class="nav-item active" onclick="showSection('overview')">
                    <span class="nav-icon">📊</span>
                    Overview
                </a>
                <a href="#" class="nav-item" onclick="showSection('milestones')">
                    <span class="nav-icon">🎯</span>
                    Milestones
                </a>
                <a href="#" class="nav-item" onclick="showSection('tracker')">
                    <span class="nav-icon">📝</span>
                    Baby Tracker
                </a>
                <a href="#" class="nav-item" onclick="showSection('notes')">
                    <span class="nav-icon">📋</span>
                    My Notes
                </a>
                <a href="#" class="nav-item" onclick="showSection('profile')">
                    <span class="nav-icon">⚙️</span>
                    Profile
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <h2>Dashboard Overview</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Baby's Age</h3>
                        <p id="babyAge">Set birthdate in profile</p>
                    </div>
                    <div class="stat-card">
                        <h3>Feedings Today</h3>
                        <p id="feedingCount">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>Sleep Hours</h3>
                        <p id="sleepHours">0</p>
                    </div>
                    <div class="stat-card">
                        <h3>Milestones</h3>
                        <p id="milestoneProgress">0% Complete</p>
                    </div>
                </div>

                <div class="recent-section">
                    <h3>Recent Activities</h3>
                    <div class="activity-list" id="activityList">
                        <p>Welcome to your dashboard! Start tracking your baby's activities.</p>
                    </div>
                </div>
            </section>

            <!-- Milestones Section -->
            <section id="milestones" class="content-section">
                <h2>Milestone Tracker</h2>
                
                <div class="milestone-progress">
                    <h3>Progress Overview</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                    <p id="progressText">0% Complete</p>
                </div>

                <div class="milestone-categories">
                    <div class="milestone-category">
                        <h4>0-3 Months</h4>
                        <div class="milestone-item">
                            <input type="checkbox" id="m1" onchange="updateProgress()">
                            <label for="m1">Lifts head when on tummy</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m2" onchange="updateProgress()">
                            <label for="m2">Follows objects with eyes</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m3" onchange="updateProgress()">
                            <label for="m3">Begins to smile</label>
                        </div>
                    </div>

                    <div class="milestone-category">
                        <h4>4-6 Months</h4>
                        <div class="milestone-item">
                            <input type="checkbox" id="m4" onchange="updateProgress()">
                            <label for="m4">Rolls over</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m5" onchange="updateProgress()">
                            <label for="m5">Sits with support</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m6" onchange="updateProgress()">
                            <label for="m6">Reaches for toys</label>
                        </div>
                    </div>

                    <div class="milestone-category">
                        <h4>7-9 Months</h4>
                        <div class="milestone-item">
                            <input type="checkbox" id="m7" onchange="updateProgress()">
                            <label for="m7">Sits without support</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m8" onchange="updateProgress()">
                            <label for="m8">Crawls or scoots</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m9" onchange="updateProgress()">
                            <label for="m9">Says "mama" or "dada"</label>
                        </div>
                    </div>

                    <div class="milestone-category">
                        <h4>10-12 Months</h4>
                        <div class="milestone-item">
                            <input type="checkbox" id="m10" onchange="updateProgress()">
                            <label for="m10">Pulls to standing</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m11" onchange="updateProgress()">
                            <label for="m11">Takes first steps</label>
                        </div>
                        <div class="milestone-item">
                            <input type="checkbox" id="m12" onchange="updateProgress()">
                            <label for="m12">Says first words</label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Baby Tracker Section -->
            <section id="tracker" class="content-section">
                <h2>Baby Activity Tracker</h2>
                
                <div class="tracker-grid">
                    <div class="tracker-card">
                        <h3>Feeding</h3>
                        <button class="track-btn" onclick="logActivity('feeding')">Log Feeding</button>
                        <p>Last: <span id="lastFeeding">Not recorded</span></p>
                        <p>Today: <span id="todayFeeding">0 times</span></p>
                    </div>

                    <div class="tracker-card">
                        <h3>Sleep</h3>
                        <button class="track-btn" onclick="logActivity('sleep')">Log Sleep</button>
                        <p>Last: <span id="lastSleep">Not recorded</span></p>
                        <p>Today: <span id="todaySleep">0 hours</span></p>
                    </div>

                    <div class="tracker-card">
                        <h3>Diaper Change</h3>
                        <button class="track-btn" onclick="logActivity('diaper')">Log Change</button>
                        <p>Last: <span id="lastDiaper">Not recorded</span></p>
                        <p>Today: <span id="todayDiaper">0 times</span></p>
                    </div>

                    <div class="tracker-card">
                        <h3>Playtime</h3>
                        <button class="track-btn" onclick="logActivity('play')">Log Playtime</button>
                        <p>Last: <span id="lastPlay">Not recorded</span></p>
                        <p>Today: <span id="todayPlay">0 times</span></p>
                    </div>
                </div>

                <div class="activity-history">
                    <h3>Today's Activities</h3>
                    <div class="history-list" id="historyList">
                        <p>No activities logged today.</p>
                    </div>
                </div>
            </section>

            <!-- Notes Section -->
            <section id="notes" class="content-section">
                <h2>My Notes</h2>
                
                <div class="add-note-section">
                    <textarea id="noteInput" placeholder="Write a note about your baby..."></textarea>
                    <button class="add-note-btn" onclick="addNote()">Add Note</button>
                </div>

                <div class="notes-list" id="notesList">
                    <p>No notes yet. Add your first note above!</p>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="content-section">
                <h2>Profile Settings</h2>
                
                <form id="profileForm" onsubmit="updateProfile(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profileName">Your Name:</label>
                            <input type="text" id="profileName" required>
                        </div>
                        <div class="form-group">
                            <label for="profileEmail">Email:</label>
                            <input type="email" id="profileEmail" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="babyName">Baby's Name:</label>
                            <input type="text" id="babyName">
                        </div>
                        <div class="form-group">
                            <label for="babyBirthdate">Baby's Birthdate:</label>
                            <input type="date" id="babyBirthdate">
                        </div>
                    </div>

                    <button type="submit" class="save-btn">Save Changes</button>
                </form>

                <div class="danger-zone">
                    <h3>Danger Zone</h3>
                    <button class="delete-btn" onclick="deleteAccount()">Delete Account</button>
                </div>
            </section>
        </main>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
