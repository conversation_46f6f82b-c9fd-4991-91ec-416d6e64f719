-- Baby's First Year Database Schema
-- Created for the Baby Care Website

-- Create database
CREATE DATABASE IF NOT EXISTS baby_first_year;
USE baby_first_year;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    baby_name VARCHAR(100),
    baby_birthdate DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User sessions table
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Baby activities table
CREATE TABLE baby_activities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    activity_type ENUM('feeding', 'sleep', 'diaper', 'play') NOT NULL,
    activity_date DATE NOT NULL,
    activity_time TIME NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, activity_date),
    INDEX idx_activity_type (activity_type)
);

-- Milestones table
CREATE TABLE milestones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    milestone_name VARCHAR(200) NOT NULL,
    age_range VARCHAR(20) NOT NULL,
    description TEXT,
    category ENUM('physical', 'cognitive', 'social', 'language') NOT NULL,
    sort_order INT DEFAULT 0
);

-- User milestones (tracking which milestones each user's baby has achieved)
CREATE TABLE user_milestones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    milestone_id INT NOT NULL,
    achieved_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (milestone_id) REFERENCES milestones(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_milestone (user_id, milestone_id)
);

-- User notes table
CREATE TABLE user_notes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    note_title VARCHAR(200),
    note_content TEXT NOT NULL,
    note_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, note_date)
);

-- Growth tracking table
CREATE TABLE growth_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    measurement_date DATE NOT NULL,
    weight_kg DECIMAL(5,2),
    height_cm DECIMAL(5,2),
    head_circumference_cm DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, measurement_date)
);

-- Feeding schedule table
CREATE TABLE feeding_schedule (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    feeding_time TIME NOT NULL,
    feeding_type ENUM('breast', 'bottle', 'solid') NOT NULL,
    amount_ml INT,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Sleep tracking table
CREATE TABLE sleep_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    sleep_date DATE NOT NULL,
    bedtime TIME,
    wake_time TIME,
    total_sleep_hours DECIMAL(4,2),
    nap_count INT DEFAULT 0,
    sleep_quality ENUM('excellent', 'good', 'fair', 'poor'),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, sleep_date)
);

-- Vaccination records table
CREATE TABLE vaccinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    vaccine_name VARCHAR(100) NOT NULL,
    vaccination_date DATE NOT NULL,
    next_due_date DATE,
    doctor_name VARCHAR(100),
    clinic_name VARCHAR(150),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, vaccination_date)
);

-- Doctor appointments table
CREATE TABLE appointments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    doctor_name VARCHAR(100) NOT NULL,
    clinic_name VARCHAR(150),
    appointment_type VARCHAR(100),
    notes TEXT,
    status ENUM('scheduled', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, appointment_date)
);

-- Baby photos table
CREATE TABLE baby_photos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    photo_filename VARCHAR(255) NOT NULL,
    photo_title VARCHAR(200),
    photo_description TEXT,
    photo_date DATE NOT NULL,
    baby_age_months INT,
    is_milestone_photo BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, photo_date)
);

-- Tips and articles table
CREATE TABLE tips_articles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category ENUM('feeding', 'sleep', 'development', 'health', 'safety') NOT NULL,
    age_range VARCHAR(20),
    author VARCHAR(100),
    is_published BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_age_range (age_range)
);

-- User preferences table
CREATE TABLE user_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    date_format VARCHAR(20) DEFAULT 'YYYY-MM-DD',
    time_format VARCHAR(10) DEFAULT '24h',
    weight_unit ENUM('kg', 'lbs') DEFAULT 'kg',
    height_unit ENUM('cm', 'inches') DEFAULT 'cm',
    notifications_enabled BOOLEAN DEFAULT TRUE,
    email_reminders BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_activities_user_type ON baby_activities(user_id, activity_type);
CREATE INDEX idx_notes_user ON user_notes(user_id);
CREATE INDEX idx_milestones_age ON milestones(age_range);
CREATE INDEX idx_user_milestones_user ON user_milestones(user_id);

-- Create views for common queries
CREATE VIEW user_activity_summary AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.baby_name,
    COUNT(ba.id) as total_activities,
    COUNT(CASE WHEN ba.activity_type = 'feeding' THEN 1 END) as feeding_count,
    COUNT(CASE WHEN ba.activity_type = 'sleep' THEN 1 END) as sleep_count,
    COUNT(CASE WHEN ba.activity_type = 'diaper' THEN 1 END) as diaper_count,
    COUNT(CASE WHEN ba.activity_type = 'play' THEN 1 END) as play_count
FROM users u
LEFT JOIN baby_activities ba ON u.id = ba.user_id
GROUP BY u.id, u.name, u.baby_name;

CREATE VIEW milestone_progress AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.baby_name,
    COUNT(m.id) as total_milestones,
    COUNT(um.id) as achieved_milestones,
    ROUND((COUNT(um.id) / COUNT(m.id)) * 100, 2) as progress_percentage
FROM users u
CROSS JOIN milestones m
LEFT JOIN user_milestones um ON u.id = um.user_id AND m.id = um.milestone_id
GROUP BY u.id, u.name, u.baby_name;
